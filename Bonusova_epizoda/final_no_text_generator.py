#!/usr/bin/env python3
"""
Finálny generátor bez textu - spracuje všetky epizódy s AI analýzou
"""

import os
import re
from openai import OpenAI
import requests
from datetime import datetime
import time

class FinalNoTextGenerator:
    def __init__(self, api_key):
        self.client = OpenAI(api_key=api_key)
        
        # Cesta k čistým príbehom
        self.stories_dir = "/Users/<USER>/Desktop/Krvavý Audio/clean_stories"
        
        # Výstupný priečinok na desktop
        desktop_path = os.path.expanduser("~/Desktop")
        self.output_dir = os.path.join(desktop_path, "Krvavý_Dobšinský_FINAL_No_Text_Artwork")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Retro štýl BEZ TEXTU
        self.retro_style = """Retro fairytale horror style, flat illustration, parchment beige and blood red colors, geometric shapes, minimalist composition, Eastern European folk tale aesthetic, vector silhouettes, no gradients, NO TEXT, NO LETTERS, NO WORDS, NO WRITING, pure visual illustration only"""
    
    def analyze_story_content(self, story_text, episode_name):
        """Analyzuje obsah príbehu pomocí AI"""
        
        # Skrátenie textu ak je príliš dlhý
        if len(story_text) > 3000:
            story_text = story_text[:3000] + "..."
        
        analysis_prompt = f"""
Analyzuj tento slovenský horror príbeh a extrahuj JENA vizuálne elementy pre ilustráciu:

PRÍBEH: "{story_text}"

Extrahuj v angličtine (KRÁTKO):
1. HLAVNÁ POSTAVA (max 20 znakov)
2. LOKÁCIA (max 20 znakov) 
3. ATMOSFÉRA (max 30 znakov)
4. KĽÚČOVÝ OBJEKT (max 20 znakov)

Formát:
CHARACTER: [postava]
LOCATION: [lokácia]
MOOD: [atmosféra]
OBJECT: [objekt]

Buď VEĽMI stručný!
"""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "Si expert na stručnú analýzu horror príbehov pre vizuálne ilustrácie. Odpovedaj VEĽMI krátko."},
                    {"role": "user", "content": analysis_prompt}
                ],
                max_tokens=200,
                temperature=0.3
            )
            
            analysis = response.choices[0].message.content
            return self.parse_analysis(analysis)
            
        except Exception as e:
            print(f"   ⚠️  Chyba pri AI analýze: {e}")
            return self.simple_fallback(story_text)
    
    def parse_analysis(self, analysis_text):
        """Parsuje AI analýzu"""
        elements = {
            'character': '',
            'location': '',
            'mood': '',
            'object': ''
        }
        
        patterns = {
            'character': r'CHARACTER:\s*(.+?)(?=\n|$)',
            'location': r'LOCATION:\s*(.+?)(?=\n|$)',
            'mood': r'MOOD:\s*(.+?)(?=\n|$)',
            'object': r'OBJECT:\s*(.+?)(?=\n|$)'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, analysis_text, re.IGNORECASE)
            if match:
                elements[key] = match.group(1).strip()[:30]  # Max 30 znakov
        
        return elements
    
    def simple_fallback(self, story_text):
        """Jednoduchá analýza ako fallback"""
        text_lower = story_text.lower()
        
        character = 'mysterious figure'
        location = 'dark place'
        mood = 'eerie, dark'
        obj = 'shadows'
        
        # Detekcia postáv
        if 'žena' in text_lower or 'woman' in text_lower:
            character = 'woman'
        elif 'muž' in text_lower or 'man' in text_lower:
            character = 'man'
        elif 'dieťa' in text_lower or 'child' in text_lower:
            character = 'child'
        elif 'duch' in text_lower or 'ghost' in text_lower:
            character = 'ghost'
        elif 'upír' in text_lower or 'vampire' in text_lower:
            character = 'vampire'
        
        # Detekcia lokácií
        if 'hrad' in text_lower or 'castle' in text_lower:
            location = 'castle'
        elif 'les' in text_lower or 'forest' in text_lower:
            location = 'forest'
        elif 'dom' in text_lower or 'house' in text_lower:
            location = 'house'
        elif 'cintorín' in text_lower or 'cemetery' in text_lower:
            location = 'cemetery'
        
        return {
            'character': character,
            'location': location,
            'mood': mood,
            'object': obj
        }
    
    def create_no_text_prompt(self, elements):
        """Vytvorí prompt bez textu"""
        
        # Zostavenie scény
        scene_parts = []
        
        if elements['location']:
            scene_parts.append(f"in {elements['location'][:20]}")
        
        if elements['character']:
            scene_parts.append(f"featuring {elements['character'][:20]}")
        
        if elements['object']:
            scene_parts.append(f"with {elements['object'][:20]}")
        
        main_scene = ', '.join(scene_parts) if scene_parts else "horror scene"
        mood = elements['mood'][:30] if elements['mood'] else 'dark, eerie'
        
        # Finálny prompt s explicitným zákazom textu
        prompt = f"{main_scene}, {mood}, {self.retro_style}"
        
        # Kontrola dĺžky
        if len(prompt) > 950:
            main_scene = main_scene[:80]
            mood = mood[:20]
            prompt = f"{main_scene}, {mood}, {self.retro_style}"
        
        return prompt
    
    def generate_no_text_image(self, episode_name, prompt):
        """Generuje obrázok bez textu"""
        
        print(f"   🎨 Generujem obrázok BEZ TEXTU...")
        
        try:
            response = self.client.images.generate(
                prompt=prompt,
                n=1,
                size="1024x1024",
                quality="standard",
                model="dall-e-3"
            )
            
            image_url = response.data[0].url
            print(f"   ✅ Vygenerované!")
            
            return self._save_image(image_url, episode_name)
            
        except Exception as e:
            print(f"   ❌ Chyba s DALL-E 3: {e}")
            
            try:
                print(f"   🔄 Skúšam DALL-E 2...")
                response = self.client.images.generate(
                    prompt=prompt,
                    n=1,
                    size="1024x1024",
                    model="dall-e-2"
                )
                
                image_url = response.data[0].url
                print(f"   ✅ Vygenerované (DALL-E 2)!")
                
                return self._save_image(image_url, episode_name)
                
            except Exception as e2:
                print(f"   ❌ Chyba aj s DALL-E 2: {e2}")
                return None
    
    def _save_image(self, image_url, episode_name):
        """Uloží obrázok"""
        try:
            image_response = requests.get(image_url, timeout=30)
            
            if image_response.status_code == 200:
                safe_name = re.sub(r'[^\w\-_]', '_', episode_name)
                filename = f"{safe_name}_NO_TEXT_retro.png"
                filepath = os.path.join(self.output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_response.content)
                
                print(f"   💾 Uložené: {filename}")
                return filepath
            else:
                print(f"   ❌ Chyba pri sťahovaní: {image_response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_all_stories(self, max_stories=None):
        """Spracuje všetky príbehy"""
        
        print("🚫 === FINÁLNY GENERÁTOR BEZ TEXTU ===")
        print(f"📁 Zdroj: {self.stories_dir}")
        print(f"📁 Výstup: {self.output_dir}")
        print("🎯 Každý obrázok bude BEZ TEXTU!")
        print()
        
        if not os.path.exists(self.stories_dir):
            print(f"❌ Priečinok s príbehmi neexistuje!")
            return
        
        # Nájdenie súborov
        story_files = []
        for file in os.listdir(self.stories_dir):
            if file.endswith('.txt'):
                story_files.append(os.path.join(self.stories_dir, file))
        
        if not story_files:
            print("❌ Nenašli sa žiadne súbory s príbehmi!")
            return
        
        story_files = sorted(story_files)
        if max_stories:
            story_files = story_files[:max_stories]
        
        print(f"📊 Spracovávam {len(story_files)} príbehov")
        print()
        
        successful = 0
        failed = 0
        
        for i, file_path in enumerate(story_files, 1):
            filename = os.path.basename(file_path)
            episode_name = filename.replace('.txt', '')
            
            print(f"[{i}/{len(story_files)}] {episode_name}")
            
            try:
                # Načítanie príbehu
                with open(file_path, 'r', encoding='utf-8') as f:
                    story_text = f.read().strip()
                
                if len(story_text) < 100:
                    print(f"   ⚠️  Príbeh príliš krátky")
                    failed += 1
                    continue
                
                print(f"   📖 Analyzujem príbeh ({len(story_text)} znakov)")
                
                # AI analýza
                elements = self.analyze_story_content(story_text, episode_name)
                
                print(f"   🎭 Elementy: {elements['character']}, {elements['location']}, {elements['mood']}")
                
                # Vytvorenie promptu bez textu
                prompt = self.create_no_text_prompt(elements)
                
                # Generovanie
                result = self.generate_no_text_image(episode_name, prompt)
                
                if result:
                    successful += 1
                    print(f"   ✅ Úspech!")
                else:
                    failed += 1
                    print(f"   ❌ Neúspech")
                
                # Pauza
                if i < len(story_files):
                    print("   ⏳ Pauza 3 sekundy...")
                    time.sleep(3)
                
            except Exception as e:
                print(f"   ❌ Chyba: {e}")
                failed += 1
            
            print()
        
        # Finálny súhrn
        print("🏁 === FINÁLNE VÝSLEDKY ===")
        print(f"📊 Spracované: {len(story_files)}")
        print(f"✅ Úspešné: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📁 Obrázky BEZ TEXTU uložené v: {self.output_dir}")
        
        return successful, failed

def main():
    """Hlavná funkcia"""
    print("🚫 === FINAL NO TEXT GENERATOR ===")
    print("Generuje obrázky BEZ TEXTU na základe AI analýzy príbehov")
    print()
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        return
    
    generator = FinalNoTextGenerator(api_key)
    
    max_stories = input("Koľko príbehov spracovať? (Enter = všetky): ").strip()
    if max_stories:
        try:
            max_stories = int(max_stories)
        except:
            max_stories = None
    else:
        max_stories = None
    
    print(f"🚀 Spúšťam generovanie...")
    print()
    
    generator.process_all_stories(max_stories)

if __name__ == "__main__":
    main()
