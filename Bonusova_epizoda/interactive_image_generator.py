#!/usr/bin/env python3
"""
Interaktívny generátor o<PERSON><PERSON><PERSON><PERSON> s možnosťou zadania API kľúča
"""

import os
import openai
import requests
from datetime import datetime
import getpass

def get_api_key():
    """Získa API kľúč od používateľa"""
    
    # Skús načítať z environment
    api_key = os.getenv('OPENAI_API_KEY')
    
    if api_key:
        print(f"✅ API kľúč nájdený v environment")
        return api_key
    
    # Skús načítať z .env súboru
    if os.path.exists('.env'):
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('OPENAI_API_KEY=') and not line.startswith('#'):
                        api_key = line.split('=', 1)[1].strip()
                        if api_key and api_key != 'sk-your-openai-api-key-here':
                            print(f"✅ API kľúč nájdený v .env súbore")
                            return api_key
        except Exception as e:
            print(f"⚠️  Chyba pri čítaní .env: {e}")
    
    # Požiadaj používateľa o zadanie
    print("🔑 OpenAI API kľúč nie je nastavený")
    print("📝 Môžete ho získať na: https://platform.openai.com/api-keys")
    print()
    
    api_key = getpass.getpass("Zadajte váš OpenAI API kľúč (sk-...): ")
    
    if not api_key or not api_key.startswith('sk-'):
        print("❌ Neplatný API kľúč!")
        return None
    
    return api_key

def generate_image_from_prompt(api_key, prompt, filename_prefix="test"):
    """Generuje obrázok z promptu"""
    
    openai.api_key = api_key
    
    # Vytvorenie výstupného priečinka
    output_dir = "generated_images"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"🎨 Generujem obrázok...")
    print(f"📝 Prompt: {prompt}")
    print()
    
    try:
        # Pokus o DALL-E 3
        print("🔄 Skúšam DALL-E 3...")
        response = openai.Image.create(
            prompt=prompt,
            n=1,
            size="1024x1024",
            quality="standard",
            model="dall-e-3"
        )
        
        model_used = "DALL-E 3"
        
    except Exception as e:
        print(f"⚠️  DALL-E 3 nedostupné: {e}")
        print("🔄 Skúšam DALL-E 2...")
        
        try:
            response = openai.Image.create(
                prompt=prompt,
                n=1,
                size="1024x1024"
            )
            model_used = "DALL-E 2"
            
        except Exception as e2:
            print(f"❌ Chyba aj s DALL-E 2: {e2}")
            return None
    
    try:
        # Získanie URL
        image_url = response['data'][0]['url']
        print(f"✅ Obrázok vygenerovaný pomocou {model_used}!")
        print(f"🔗 URL: {image_url}")
        
        # Stiahnutie
        print("📥 Sťahujem obrázok...")
        image_response = requests.get(image_url, timeout=30)
        
        if image_response.status_code == 200:
            # Uloženie
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{timestamp}.png"
            filepath = os.path.join(output_dir, filename)
            
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            
            print(f"💾 Obrázok uložený: {filepath}")
            print(f"🖼️  Môžete ho otvoriť pomocou: open {filepath}")
            
            return filepath
            
        else:
            print(f"❌ Chyba pri sťahovaní: {image_response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Chyba pri spracovaní: {e}")
        return None

def main():
    """Hlavná funkcia"""
    print("🎨 === OPENAI DALL-E IMAGE GENERATOR ===")
    print("Pre Krvavý Dobšinský podcast")
    print()
    
    # Získanie API kľúča
    api_key = get_api_key()
    if not api_key:
        print("❌ Bez API kľúča nemôžem pokračovať")
        return
    
    # Ukážkové prompty z Midjourney generátora
    sample_prompts = {
        "1": {
            "name": "Baba Jaga",
            "prompt": "dark forest with old wooden house, witch hut, mysterious atmosphere, horror scene, dramatic lighting, detailed, realistic style"
        },
        "2": {
            "name": "Upír z hradu", 
            "prompt": "gothic castle at night, vampire figure, dark atmosphere, medieval architecture, dramatic shadows, horror scene, realistic style"
        },
        "3": {
            "name": "Vlkolak v lese",
            "prompt": "dark forest, werewolf creature, moonlight, scary atmosphere, detailed fur, horror scene, realistic style"
        },
        "4": {
            "name": "Slovenský folklór",
            "prompt": "Slovak folk horror tale, traditional village, mysterious figure, rural setting, folk art style, atmospheric"
        },
        "5": {
            "name": "Vlastný prompt",
            "prompt": "custom"
        }
    }
    
    print("📋 Vyberte prompt:")
    for key, value in sample_prompts.items():
        print(f"  {key}. {value['name']}")
    print()
    
    choice = input("Vaša voľba (1-5): ").strip()
    
    if choice in sample_prompts:
        selected = sample_prompts[choice]
        
        if selected['prompt'] == 'custom':
            prompt = input("Zadajte váš vlastný prompt: ").strip()
            if not prompt:
                print("❌ Prázdny prompt!")
                return
            filename_prefix = "custom"
        else:
            prompt = selected['prompt']
            filename_prefix = selected['name'].lower().replace(' ', '_')
        
        print()
        print(f"🎯 Vybraný prompt: {selected['name']}")
        print(f"📝 Text: {prompt}")
        print()
        
        # Generovanie
        result = generate_image_from_prompt(api_key, prompt, filename_prefix)
        
        if result:
            print()
            print("🎉 Úspech! Obrázok bol vygenerovaný a uložený.")
        else:
            print()
            print("❌ Nepodarilo sa vygenerovať obrázok.")
    
    else:
        print("❌ Neplatná voľba!")

if __name__ == "__main__":
    main()
