#!/usr/bin/env python3
"""
Test generovania jedného obrázka pomocou OpenAI DALL-E
"""

import os
import openai
import requests
from datetime import datetime

# Pokus o načítanie .env súboru
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv nie je nain<PERSON>talované, skúsime manuálne načítanie
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

def generate_single_image():
    """Generuje jeden testovací obrázok"""
    
    # Kontrola API kľúča
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        print("Nastavte ho pomocou: export OPENAI_API_KEY='váš_kľú<PERSON>'")
        return
    
    openai.api_key = api_key
    
    # Vytvorenie výstupného priečinka
    output_dir = "test_images"
    os.makedirs(output_dir, exist_ok=True)
    
    # Ukážkový prompt z Baba Jaga epizódy (konvertovaný pre DALL-E)
    prompt = "dark forest with old wooden house, witch hut, mysterious atmosphere, horror scene, dramatic lighting, detailed, realistic style"
    
    print("🎨 === TEST GENEROVANIA OBRÁZKA ===")
    print(f"📝 Prompt: {prompt}")
    print(f"📁 Výstup: {output_dir}/")
    print()
    
    try:
        print("🔄 Generujem obrázok...")
        
        # Generovanie pomocou DALL-E 3
        response = openai.Image.create(
            prompt=prompt,
            n=1,
            size="1024x1024",
            quality="standard",
            model="dall-e-3"
        )
        
        # Získanie URL
        image_url = response['data'][0]['url']
        print(f"✅ Obrázok vygenerovaný!")
        print(f"🔗 URL: {image_url}")
        
        # Stiahnutie
        print("📥 Sťahujem obrázok...")
        image_response = requests.get(image_url)
        
        if image_response.status_code == 200:
            # Uloženie
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"baba_jaga_test_{timestamp}.png"
            filepath = os.path.join(output_dir, filename)
            
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            
            print(f"💾 Obrázok uložený: {filepath}")
            print(f"🖼️  Môžete ho otvoriť pomocou: open {filepath}")
            
        else:
            print(f"❌ Chyba pri sťahovaní: {image_response.status_code}")
    
    except Exception as e:
        print(f"❌ Chyba: {e}")
        
        # Ak je problém s DALL-E 3, skúsime DALL-E 2
        if "dall-e-3" in str(e).lower():
            print("🔄 Skúšam DALL-E 2...")
            try:
                response = openai.Image.create(
                    prompt=prompt,
                    n=1,
                    size="1024x1024"
                )
                
                image_url = response['data'][0]['url']
                print(f"✅ Obrázok vygenerovaný (DALL-E 2)!")
                
                # Stiahnutie
                image_response = requests.get(image_url)
                if image_response.status_code == 200:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"baba_jaga_dalle2_{timestamp}.png"
                    filepath = os.path.join(output_dir, filename)
                    
                    with open(filepath, 'wb') as f:
                        f.write(image_response.content)
                    
                    print(f"💾 Obrázok uložený: {filepath}")
                
            except Exception as e2:
                print(f"❌ Chyba aj s DALL-E 2: {e2}")

if __name__ == "__main__":
    generate_single_image()
