#!/usr/bin/env python3
"""
Inteligentný analyzátor pr<PERSON><PERSON>hov pre Krvavý Dobšinský
Analyzuje obsah epizód a vytvára jedinečné, obsahovo bohaté prompty
"""

import os
import re
from openai import OpenAI
import requests
from datetime import datetime
import time

class IntelligentStoryAnalyzer:
    def __init__(self, api_key):
        self.client = OpenAI(api_key=api_key)
        
        # Cesta k čistým príbehom
        self.stories_dir = "/Users/<USER>/Desktop/Krvavý Audio/clean_stories"
        
        # Výstupný priečinok na desktop
        desktop_path = os.path.expanduser("~/Desktop")
        self.output_dir = os.path.join(desktop_path, "Krvavý_Dobšinský_Unique_Retro_Artwork")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Retro fairytale horror štýl (skrátený pre API limit, BEZ TEXTU!)
        self.retro_style = """Retro fairytale horror style, flat illustration, parchment beige and blood red colors, geometric shapes, minimalist composition, Eastern European folk tale aesthetic, vector silhouettes, no gradients, NO TEXT, NO LETTERS, NO WORDS, pure visual illustration only."""
    
    def analyze_story_content(self, story_text, episode_name):
        """Analyzuje obsah príbehu pomocí AI a extrahuje kľúčové vizuálne elementy"""
        
        # Skrátenie textu ak je príliš dlhý
        if len(story_text) > 3000:
            story_text = story_text[:3000] + "..."
        
        analysis_prompt = f"""
Analyzuj tento slovenský horror príbeh z podcastu "Krvavý Dobšinský" a extrahuj kľúčové vizuálne elementy pre vytvorenie ilustrácie:

PRÍBEH: "{story_text}"

EPIZÓDA: {episode_name}

Extrahuj a opíš v angličtine:
1. HLAVNÁ POSTAVA/POSTAVY (kto je protagonista, antagonista)
2. KĽÚČOVÁ LOKÁCIA (kde sa dej odohráva)
3. ATMOSFÉRA A NÁLADA (aká je atmosféra príbehu)
4. ŠPECIFICKÉ OBJEKTY/SYMBOLY (dôležité predmety v príbehu)
5. KĽÚČOVÁ SCÉNA (najdôležitejší moment príbehu)

Odpoveď formátuj ako:
CHARACTERS: [postavy]
LOCATION: [lokácia]
ATMOSPHERE: [atmosféra]
OBJECTS: [objekty]
KEY_SCENE: [kľúčová scéna]

Buď stručný ale výstižný. Zameraj sa na vizuálne elementy vhodné pre ilustráciu.
"""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "Si expert na analýzu horror príbehov a vytvorenie vizuálnych opisov pre ilustrácie."},
                    {"role": "user", "content": analysis_prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            analysis = response.choices[0].message.content
            return self.parse_analysis(analysis)
            
        except Exception as e:
            print(f"   ⚠️  Chyba pri analýze AI: {e}")
            # Fallback na jednoduchú analýzu
            return self.simple_analysis(story_text, episode_name)
    
    def parse_analysis(self, analysis_text):
        """Parsuje AI analýzu do štruktúry"""
        elements = {
            'characters': '',
            'location': '',
            'atmosphere': '',
            'objects': '',
            'key_scene': ''
        }
        
        patterns = {
            'characters': r'CHARACTERS:\s*(.+?)(?=\n[A-Z]+:|$)',
            'location': r'LOCATION:\s*(.+?)(?=\n[A-Z]+:|$)',
            'atmosphere': r'ATMOSPHERE:\s*(.+?)(?=\n[A-Z]+:|$)',
            'objects': r'OBJECTS:\s*(.+?)(?=\n[A-Z]+:|$)',
            'key_scene': r'KEY_SCENE:\s*(.+?)(?=\n[A-Z]+:|$)'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, analysis_text, re.DOTALL | re.IGNORECASE)
            if match:
                elements[key] = match.group(1).strip()
        
        return elements
    
    def simple_analysis(self, story_text, episode_name):
        """Jednoduchá analýza ako fallback"""
        text_lower = story_text.lower()
        
        # Základné elementy
        characters = []
        locations = []
        atmosphere = []
        objects = []
        
        # Detekcia postav
        character_keywords = {
            'žena': 'woman', 'muž': 'man', 'dieťa': 'child', 'starec': 'old man',
            'stará': 'old woman', 'duch': 'ghost', 'upír': 'vampire',
            'vlkolak': 'werewolf', 'čarodejnica': 'witch', 'démon': 'demon'
        }
        
        for slovak, english in character_keywords.items():
            if slovak in text_lower:
                characters.append(english)
        
        # Detekcia lokácií
        location_keywords = {
            'hrad': 'castle', 'dom': 'house', 'les': 'forest',
            'cintorín': 'cemetery', 'kostol': 'church', 'dedina': 'village'
        }
        
        for slovak, english in location_keywords.items():
            if slovak in text_lower:
                locations.append(english)
        
        return {
            'characters': ', '.join(characters[:2]) if characters else 'mysterious figure',
            'location': locations[0] if locations else 'dark place',
            'atmosphere': 'dark, mysterious, horror',
            'objects': 'shadows, darkness',
            'key_scene': f"horror scene in {locations[0] if locations else 'mysterious place'}"
        }
    
    def create_unique_prompt(self, elements, episode_name):
        """Vytvorí jedinečný prompt na základe analýzy"""

        # Zostavenie scény na základe analýzy (skrátené verzie)
        scene_parts = []

        if elements['location']:
            # Skrátenie lokácie na max 30 znakov
            location = elements['location'][:30] if len(elements['location']) > 30 else elements['location']
            scene_parts.append(f"in {location}")

        if elements['characters']:
            # Skrátenie postáv na max 40 znakov
            characters = elements['characters'][:40] if len(elements['characters']) > 40 else elements['characters']
            scene_parts.append(f"featuring {characters}")

        if elements['objects']:
            # Skrátenie objektov na max 30 znakov
            objects = elements['objects'][:30] if len(elements['objects']) > 30 else elements['objects']
            scene_parts.append(f"with {objects}")

        # Hlavná scéna
        main_scene = ', '.join(scene_parts) if scene_parts else "horror scene"

        # Atmosféra (skrátená)
        atmosphere = elements['atmosphere'][:50] if elements['atmosphere'] and len(elements['atmosphere']) > 50 else (elements['atmosphere'] or 'dark, mysterious')

        # Finálny prompt (kontrola dĺžky) s explicitným zákazom textu
        unique_prompt = f"{main_scene}, {atmosphere}, {self.retro_style}"

        # Ak je stále príliš dlhý, skrátime ešte viac
        if len(unique_prompt) > 900:
            main_scene = main_scene[:100]
            atmosphere = atmosphere[:30]
            unique_prompt = f"{main_scene}, {atmosphere}, {self.retro_style}"

        # Pridanie dodatočného zákazu textu na koniec
        if "NO TEXT" not in unique_prompt:
            unique_prompt += ", NO TEXT OR LETTERS"

        return unique_prompt
    
    def process_story_file(self, file_path):
        """Spracuje jeden súbor s príbehom"""
        
        episode_name = os.path.basename(file_path).replace('.txt', '')
        
        try:
            # Načítanie príbehu
            with open(file_path, 'r', encoding='utf-8') as f:
                story_text = f.read().strip()
            
            if len(story_text) < 100:
                print(f"   ⚠️  Príbeh príliš krátky")
                return None
            
            print(f"   📖 Analyzujem príbeh ({len(story_text)} znakov)")
            
            # AI analýza obsahu
            elements = self.analyze_story_content(story_text, episode_name)
            
            print(f"   🎭 Kľúčové elementy:")
            print(f"      • Postavy: {elements['characters']}")
            print(f"      • Lokácia: {elements['location']}")
            print(f"      • Atmosféra: {elements['atmosphere']}")
            
            # Vytvorenie jedinečného promptu
            unique_prompt = self.create_unique_prompt(elements, episode_name)
            
            # Generovanie obrázka
            return self.generate_unique_image(episode_name, unique_prompt)
            
        except Exception as e:
            print(f"   ❌ Chyba pri spracovaní: {e}")
            return None
    
    def generate_unique_image(self, episode_name, prompt):
        """Generuje jedinečný obrázok"""
        
        print(f"   🎨 Generujem jedinečný obrázok...")
        
        try:
            # Generovanie pomocou DALL-E 3
            response = self.client.images.generate(
                prompt=prompt,
                n=1,
                size="1024x1024",
                quality="standard",
                model="dall-e-3"
            )
            
            image_url = response.data[0].url
            print(f"   ✅ Vygenerované!")
            
            # Stiahnutie a uloženie
            return self._save_unique_image(image_url, episode_name)
            
        except Exception as e:
            print(f"   ❌ Chyba s DALL-E 3: {e}")
            
            # Pokus s DALL-E 2
            try:
                print(f"   🔄 Skúšam DALL-E 2...")
                response = self.client.images.generate(
                    prompt=prompt,
                    n=1,
                    size="1024x1024",
                    model="dall-e-2"
                )
                
                image_url = response.data[0].url
                print(f"   ✅ Vygenerované (DALL-E 2)!")
                
                return self._save_unique_image(image_url, episode_name)
                
            except Exception as e2:
                print(f"   ❌ Chyba aj s DALL-E 2: {e2}")
                return None
    
    def _save_unique_image(self, image_url, episode_name):
        """Uloží jedinečný obrázok"""
        try:
            image_response = requests.get(image_url, timeout=30)
            
            if image_response.status_code == 200:
                # Vytvorenie názvu súboru
                safe_name = re.sub(r'[^\w\-_]', '_', episode_name)
                filename = f"{safe_name}_unique_retro.png"
                filepath = os.path.join(self.output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_response.content)
                
                print(f"   💾 Uložené: {filename}")
                return filepath
            else:
                print(f"   ❌ Chyba pri sťahovaní: {image_response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ Chyba pri ukladaní: {e}")
            return None

    def process_all_stories(self, max_stories=10):
        """Spracuje všetky príbehy s inteligentnou analýzou"""

        print("🧠 === INTELIGENTNÁ ANALÝZA PRÍBEHOV ===")
        print(f"📁 Zdroj: {self.stories_dir}")
        print(f"📁 Výstup: {self.output_dir}")
        print()

        if not os.path.exists(self.stories_dir):
            print(f"❌ Priečinok s príbehmi neexistuje: {self.stories_dir}")
            return

        # Nájdenie súborov s príbehmi
        story_files = []
        for file in os.listdir(self.stories_dir):
            if file.endswith('.txt'):
                story_files.append(os.path.join(self.stories_dir, file))

        if not story_files:
            print("❌ Nenašli sa žiadne súbory s príbehmi!")
            return

        # Obmedzenie počtu pre test
        story_files = sorted(story_files)[:max_stories]

        print(f"📊 Spracovávam {len(story_files)} príbehov")
        print("🎯 Každý príbeh bude analyzovaný AI pre jedinečný obsah")
        print()

        successful = 0
        failed = 0

        for i, file_path in enumerate(story_files, 1):
            filename = os.path.basename(file_path)
            episode_name = filename.replace('.txt', '')

            print(f"[{i}/{len(story_files)}] {episode_name}")

            result = self.process_story_file(file_path)

            if result:
                successful += 1
                print(f"   ✅ Úspech!")
            else:
                failed += 1
                print(f"   ❌ Neúspech")

            # Pauza medzi generovaním
            if i < len(story_files):
                print("   ⏳ Pauza 5 sekúnd...")
                time.sleep(5)

            print()

        # Finálny súhrn
        print("🏁 === VÝSLEDKY INTELIGENTNEJ ANALÝZY ===")
        print(f"📊 Spracované príbehy: {len(story_files)}")
        print(f"✅ Úspešné: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📁 Jedinečné obrázky uložené v: {self.output_dir}")
        print()
        print("🎨 Každý obrázok je vytvorený na základe:")
        print("   • AI analýzy obsahu príbehu")
        print("   • Extrakcie kľúčových vizuálnych elementov")
        print("   • Jedinečnej kompozície pre každú epizódu")
        print("   • Konzistentného retro fairytale horror štýlu")

        return successful, failed

def main():
    """Hlavná funkcia"""
    print("🧠 === INTELLIGENT STORY ANALYZER ===")
    print("Analyzuje obsah epizód a vytvára jedinečné retro obrázky")
    print()

    # Kontrola API kľúča
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        return

    analyzer = IntelligentStoryAnalyzer(api_key)

    print("🎭 Proces:")
    print("1. 📖 Načítanie obsahu každej epizódy")
    print("2. 🧠 AI analýza príbehu (postavy, lokácia, atmosféra)")
    print("3. 🎨 Vytvorenie jedinečného promptu")
    print("4. 🖼️  Generovanie obrázka v retro štýle")
    print()

    max_stories = input("Koľko príbehov spracovať? (Enter = 10): ").strip()
    if not max_stories:
        max_stories = 10
    else:
        try:
            max_stories = int(max_stories)
        except:
            max_stories = 10

    print(f"🚀 Spúšťam analýzu {max_stories} príbehov...")
    print()

    analyzer.process_all_stories(max_stories)

if __name__ == "__main__":
    main()
