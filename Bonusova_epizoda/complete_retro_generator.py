#!/usr/bin/env python3
"""
Kompletný Retro Style Generator pre Krvavý Dobšinský
Konvertuje Midjourney prompty do konzistentného retro fairytale horror štýlu
"""

import os
from openai import OpenAI
import requests
from datetime import datetime
import time

class CompleteRetroGenerator:
    def __init__(self, api_key):
        self.client = OpenAI(api_key=api_key)
        self.output_dir = "podcast_retro_artwork"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Konzistentný retro fairytale horror štýl pre podcast
        self.style_template = """Stylized flat illustration in retro fairytale horror aesthetic, using a limited color palette of parchment beige (#D2B48C), blood red (#750000), dark brown (#2C1A0C), and small golden accents. Simplified geometric shapes, soft paper texture background, minimalist composition. Imagery should feel mysterious, folkloric, and slightly unsettling, inspired by Eastern European folk tales. No gradients, no photorealism – only vector-style silhouettes and symbolic elements. Consistent visual style for podcast episode artwork."""
        
        # Mapovanie slovenských pojmov na anglické pre lepšie výsledky
        self.translation_map = {
            'hrad': 'castle', 'kaštieľ': 'manor', 'dom': 'house',
            'les': 'forest', 'cintorín': 'cemetery', 'kostol': 'church',
            'dedina': 'village', 'upír': 'vampire', 'vlkolak': 'werewolf',
            'čarodejnica': 'witch', 'duch': 'ghost', 'démon': 'demon',
            'krv': 'blood', 'tma': 'darkness', 'hmla': 'fog',
            'mesiac': 'moon', 'hviezdy': 'stars'
        }
    
    def convert_midjourney_to_retro(self, midjourney_prompt):
        """Konvertuje Midjourney prompt na retro štýl"""
        
        # Odstránenie Midjourney parametrov
        clean_prompt = midjourney_prompt.split('--')[0].strip()
        
        # Odstránenie technických termínov
        replacements = {
            'photorealistic': '',
            '8k resolution': '',
            'cinematic lighting': 'dramatic lighting',
            'highly detailed': '',
            'dramatic shadows': 'simple shadows'
        }
        
        for old, new in replacements.items():
            clean_prompt = clean_prompt.replace(old, new)
        
        # Preklad slovenských pojmov
        for slovak, english in self.translation_map.items():
            clean_prompt = clean_prompt.replace(slovak, english)
        
        # Skrátenie ak je príliš dlhý
        if len(clean_prompt) > 200:
            clean_prompt = clean_prompt[:200] + "..."
        
        # Pridanie retro štýlu
        return f"{clean_prompt}, {self.style_template}"
    
    def generate_retro_image(self, episode_name, original_prompt):
        """Generuje retro obrázok pre epizódu"""
        
        print(f"🎨 Generujem: {episode_name}")
        print(f"📝 Originál: {original_prompt[:100]}...")
        
        # Konverzia na retro štýl
        retro_prompt = self.convert_midjourney_to_retro(original_prompt)
        
        try:
            # Generovanie
            response = self.client.images.generate(
                prompt=retro_prompt,
                n=1,
                size="1024x1024",
                quality="standard",
                model="dall-e-3"
            )
            
            image_url = response.data[0].url
            print(f"   ✅ Vygenerované!")
            
            # Stiahnutie a uloženie
            return self._save_episode_image(image_url, episode_name)
            
        except Exception as e:
            print(f"   ❌ Chyba s DALL-E 3: {e}")
            
            # Pokus s DALL-E 2
            try:
                print(f"   🔄 Skúšam DALL-E 2...")
                response = self.client.images.generate(
                    prompt=retro_prompt,
                    n=1,
                    size="1024x1024",
                    model="dall-e-2"
                )
                
                image_url = response.data[0].url
                print(f"   ✅ Vygenerované (DALL-E 2)!")
                
                return self._save_episode_image(image_url, episode_name)
                
            except Exception as e2:
                print(f"   ❌ Chyba aj s DALL-E 2: {e2}")
                return None
    
    def _save_episode_image(self, image_url, episode_name):
        """Uloží obrázok epizódy"""
        try:
            image_response = requests.get(image_url, timeout=30)
            
            if image_response.status_code == 200:
                # Vytvorenie názvu súboru
                safe_name = episode_name.replace(' ', '_').replace('/', '_')
                filename = f"{safe_name}_retro_artwork.png"
                filepath = os.path.join(self.output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_response.content)
                
                print(f"   💾 Uložené: {filename}")
                return filepath
            else:
                print(f"   ❌ Chyba pri sťahovaní: {image_response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ Chyba pri ukladaní: {e}")
            return None
    
    def generate_sample_collection(self):
        """Generuje kolekciu ukážkových obrázkov"""
        
        # Ukážkové epizódy s promptmi
        sample_episodes = {
            "Baba_Jaga_kanibal": "in dark forest, old house, witch hut featuring baba yaga, dead person with door, bones, horror, fire, dark horror atmosphere",
            
            "Upir_z_hradu": "in ancient castle, gothic architecture featuring vampire, with blood, cross, darkness, gothic atmosphere, medieval atmosphere, stone textures",
            
            "Vlkolak_v_lese": "in dark forest, featuring werewolf, with bones, skull, darkness, storm, horror atmosphere",
            
            "Slovensky_folklor": "Slovak folk horror tale with witch, Slovak folklore, traditional clothing, rural setting, old house in village, mysterious atmosphere",
            
            "Hradny_duch": "medieval castle, ghost silhouette, ancient stones, mysterious atmosphere, gothic architecture"
        }
        
        print("🎨 === GENEROVANIE RETRO KOLEKCIE ===")
        print(f"📁 Výstup: {self.output_dir}/")
        print(f"🎭 Štýl: Retro fairytale horror")
        print(f"📊 Epizód: {len(sample_episodes)}")
        print()
        
        successful = 0
        failed = 0
        
        for i, (episode, prompt) in enumerate(sample_episodes.items(), 1):
            print(f"[{i}/{len(sample_episodes)}] {episode}")
            
            result = self.generate_retro_image(episode, prompt)
            
            if result:
                successful += 1
            else:
                failed += 1
            
            # Pauza medzi generovaním
            if i < len(sample_episodes):
                print("   ⏳ Pauza 3 sekundy...")
                time.sleep(3)
            
            print()
        
        # Finálny súhrn
        print("🏁 === VÝSLEDKY ===")
        print(f"✅ Úspešné: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📁 Obrázky v: {self.output_dir}/")
        
        return successful, failed

def main():
    """Hlavná funkcia"""
    print("🎨 === COMPLETE RETRO STYLE GENERATOR ===")
    print("Pre Krvavý Dobšinský podcast")
    print()
    
    # Kontrola API kľúča
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        return
    
    generator = CompleteRetroGenerator(api_key)
    
    print("🎭 Retro štýl špecifikácie:")
    print("• Farby: parchment beige (#D2B48C), blood red (#750000)")
    print("• Farby: dark brown (#2C1A0C), golden accents")
    print("• Štýl: flat illustration, geometric shapes")
    print("• Prvky: vector silhouettes, symbolic elements")
    print("• Atmosféra: Eastern European folk tales")
    print()
    
    choice = input("Generovať ukážkovú kolekciu? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes', 'ano']:
        generator.generate_sample_collection()
    else:
        print("Ukončujem...")

if __name__ == "__main__":
    main()
