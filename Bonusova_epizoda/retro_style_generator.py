#!/usr/bin/env python3
"""
Retro Fairytale Horror Style Image Generator
Generuje obrázky v konzistentnom retro štýle pre Krvavý Dobšinský podcast
"""

import os
from openai import OpenAI
import requests
from datetime import datetime

class RetroStyleGenerator:
    def __init__(self, api_key):
        self.client = OpenAI(api_key=api_key)
        self.output_dir = "retro_style_images"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Konzistentný retro fairytale horror štýl
        self.style_template = """Stylized flat illustration in retro fairytale horror aesthetic, using a limited color palette of parchment beige (#D2B48C), blood red (#750000), dark brown (#2C1A0C), and small golden accents. Simplified geometric shapes, soft paper texture background, minimalist composition. Imagery should feel mysterious, folkloric, and slightly unsettling, inspired by Eastern European folk tales. No gradients, no photorealism – only vector-style silhouettes and symbolic elements. Consistent visual style for podcast episode artwork."""
    
    def create_styled_prompt(self, base_scene):
        """Vytvorí prompt s konzistentným retro štýlom"""
        return f"{base_scene}, {self.style_template}"
    
    def generate_image(self, base_scene, filename_prefix="retro"):
        """Generuje obrázok v retro štýle"""
        
        # Vytvorenie promptu s štýlom
        full_prompt = self.create_styled_prompt(base_scene)
        
        print(f"🎨 Generujem retro štýl obrázok...")
        print(f"📝 Scéna: {base_scene}")
        print(f"🎭 Štýl: Retro fairytale horror")
        print()
        
        try:
            # Generovanie pomocou DALL-E 3
            response = self.client.images.generate(
                prompt=full_prompt,
                n=1,
                size="1024x1024",
                quality="standard",
                model="dall-e-3"
            )
            
            # Získanie URL
            image_url = response.data[0].url
            print(f"✅ Obrázok vygenerovaný!")
            
            # Stiahnutie a uloženie
            return self._download_and_save(image_url, filename_prefix)
            
        except Exception as e:
            print(f"❌ Chyba s DALL-E 3: {e}")
            
            # Pokus s DALL-E 2
            try:
                print("🔄 Skúšam DALL-E 2...")
                response = self.client.images.generate(
                    prompt=full_prompt,
                    n=1,
                    size="1024x1024",
                    model="dall-e-2"
                )
                
                image_url = response.data[0].url
                print(f"✅ Obrázok vygenerovaný (DALL-E 2)!")
                
                return self._download_and_save(image_url, filename_prefix)
                
            except Exception as e2:
                print(f"❌ Chyba aj s DALL-E 2: {e2}")
                return None
    
    def _download_and_save(self, image_url, filename_prefix):
        """Stiahne a uloží obrázok"""
        try:
            print("📥 Sťahujem obrázok...")
            image_response = requests.get(image_url, timeout=30)
            
            if image_response.status_code == 200:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{filename_prefix}_retro_{timestamp}.png"
                filepath = os.path.join(self.output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_response.content)
                
                print(f"💾 Obrázok uložený: {filepath}")
                print(f"🖼️  Otvorte pomocou: open {filepath}")
                
                return filepath
            else:
                print(f"❌ Chyba pri sťahovaní: {image_response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None

def main():
    """Hlavná funkcia"""
    print("🎨 === RETRO FAIRYTALE HORROR STYLE GENERATOR ===")
    print("Pre Krvavý Dobšinský podcast")
    print()
    
    # Získanie API kľúča
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        return
    
    generator = RetroStyleGenerator(api_key)
    
    # Ukážkové scény z podcastu
    sample_scenes = {
        "1": {
            "name": "Baba Jaga",
            "scene": "dark forest with old wooden house, witch hut, mysterious atmosphere"
        },
        "2": {
            "name": "Upír z hradu", 
            "scene": "gothic castle at night, vampire silhouette, medieval architecture"
        },
        "3": {
            "name": "Vlkolak v lese",
            "scene": "dark forest, werewolf creature silhouette, moonlight through trees"
        },
        "4": {
            "name": "Slovenský folklór",
            "scene": "traditional Slovak village, mysterious figure, rural setting"
        },
        "5": {
            "name": "Vlastná scéna",
            "scene": "custom"
        }
    }
    
    print("🎭 Retro štýl charakteristiky:")
    print("• Farby: parchment beige, blood red, dark brown, golden accents")
    print("• Štýl: flat illustration, geometric shapes, vector silhouettes")
    print("• Atmosféra: mysterious, folkloric, Eastern European folk tales")
    print()
    
    print("📋 Vyberte scénu:")
    for key, value in sample_scenes.items():
        print(f"  {key}. {value['name']}")
    print()
    
    choice = input("Vaša voľba (1-5): ").strip()
    
    if choice in sample_scenes:
        selected = sample_scenes[choice]
        
        if selected['scene'] == 'custom':
            scene = input("Zadajte vašu vlastnú scénu: ").strip()
            if not scene:
                print("❌ Prázdna scéna!")
                return
            filename_prefix = "custom"
        else:
            scene = selected['scene']
            filename_prefix = selected['name'].lower().replace(' ', '_')
        
        print()
        print(f"🎯 Vybraná scéna: {selected['name']}")
        print(f"📝 Obsah: {scene}")
        print()
        
        # Generovanie v retro štýle
        result = generator.generate_image(scene, filename_prefix)
        
        if result:
            print()
            print("🎉 Úspech! Retro štýl obrázok bol vygenerovaný!")
            print(f"📁 Uložený v: {generator.output_dir}/")
        else:
            print()
            print("❌ Nepodarilo sa vygenerovať obrázok.")
    
    else:
        print("❌ Neplatná voľba!")

if __name__ == "__main__":
    main()
