#!/usr/bin/env python3
"""
Test generátor bez textu v obrázku
"""

import os
from openai import OpenAI
import requests
from datetime import datetime

def test_no_text_generation():
    """Test generovania obrázka bez textu"""
    
    # Kontrola API kľúča
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        return
    
    client = OpenAI(api_key=api_key)
    
    # Výstupný priečinok
    desktop_path = os.path.expanduser("~/Desktop")
    output_dir = os.path.join(desktop_path, "Test_No_Text_Images")
    os.makedirs(output_dir, exist_ok=True)
    
    # Test prompt s explicitným zákazom textu
    test_prompt = """in dark forest, old wooden house, witch hut featuring young girl, old woman, eerie and terrifying atmosphere, Retro fairytale horror style, flat illustration, parchment beige and blood red colors, geometric shapes, minimalist composition, Eastern European folk tale aesthetic, vector silhouettes, no gradients, NO TEXT, NO LETTERS, NO WORDS, NO WRITING, NO SYMBOLS, pure visual illustration only"""
    
    print("🧪 === TEST GENEROVANIA BEZ TEXTU ===")
    print(f"📝 Prompt: {test_prompt[:100]}...")
    print(f"📁 Výstup: {output_dir}")
    print()
    
    try:
        print("🎨 Generujem test obrázok bez textu...")
        
        # Generovanie pomocou DALL-E 3
        response = client.images.generate(
            prompt=test_prompt,
            n=1,
            size="1024x1024",
            quality="standard",
            model="dall-e-3"
        )
        
        image_url = response.data[0].url
        print(f"✅ Obrázok vygenerovaný!")
        print(f"🔗 URL: {image_url}")
        
        # Stiahnutie a uloženie
        print("📥 Sťahujem obrázok...")
        image_response = requests.get(image_url, timeout=30)
        
        if image_response.status_code == 200:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"baba_jaga_NO_TEXT_test_{timestamp}.png"
            filepath = os.path.join(output_dir, filename)
            
            with open(filepath, 'wb') as f:
                f.write(image_response.content)
            
            print(f"💾 Obrázok uložený: {filepath}")
            print(f"🖼️  Otvorte pomocou: open '{filepath}'")
            
            # Automatické otvorenie
            os.system(f"open '{filepath}'")
            
            return filepath
        else:
            print(f"❌ Chyba pri sťahovaní: {image_response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Chyba s DALL-E 3: {e}")
        
        # Pokus s DALL-E 2
        try:
            print("🔄 Skúšam DALL-E 2...")
            response = client.images.generate(
                prompt=test_prompt,
                n=1,
                size="1024x1024",
                model="dall-e-2"
            )
            
            image_url = response.data[0].url
            print(f"✅ Obrázok vygenerovaný (DALL-E 2)!")
            
            # Stiahnutie a uloženie
            image_response = requests.get(image_url, timeout=30)
            if image_response.status_code == 200:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"baba_jaga_NO_TEXT_dalle2_{timestamp}.png"
                filepath = os.path.join(output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_response.content)
                
                print(f"💾 Obrázok uložený: {filepath}")
                print(f"🖼️  Otvorte pomocou: open '{filepath}'")
                
                # Automatické otvorenie
                os.system(f"open '{filepath}'")
                
                return filepath
            
        except Exception as e2:
            print(f"❌ Chyba aj s DALL-E 2: {e2}")
            return None

if __name__ == "__main__":
    test_no_text_generation()
