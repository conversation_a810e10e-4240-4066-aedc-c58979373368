#!/usr/bin/env python3
"""
OpenAI DALL-E Image Generator
Generuje obrázky z Midjourney promptov pomocou OpenAI API
"""

import os
import openai
import requests
from datetime import datetime

class OpenAIImageGenerator:
    def __init__(self):
        # Nastavenie API kľúča
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            print("❌ OPENAI_API_KEY nie je nastavený!")
            print("Nastavte ho pomocou: export OPENAI_API_KEY='váš_kľúč'")
            return
        
        openai.api_key = self.api_key
        
        # Výstupný priečinok pre obrázky
        self.output_dir = "generated_images"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def convert_midjourney_to_dalle(self, midjourney_prompt):
        """Konvertuje Midjourney prompt na DALL-E kompatibilný"""
        
        # Odstránenie Midjourney špecifických parametrov
        dalle_prompt = midjourney_prompt
        
        # Odstránenie --ar, --style, --v parametrov
        dalle_prompt = dalle_prompt.split('--')[0].strip()
        
        # Odstránenie príliš technických termínov
        replacements = {
            'photorealistic': 'realistic',
            '8k resolution': 'high quality',
            'cinematic lighting': 'dramatic lighting',
            'highly detailed': 'detailed'
        }
        
        for old, new in replacements.items():
            dalle_prompt = dalle_prompt.replace(old, new)
        
        # Skrátenie ak je príliš dlhý (DALL-E má limit)
        if len(dalle_prompt) > 1000:
            dalle_prompt = dalle_prompt[:1000] + "..."
        
        return dalle_prompt
    
    def generate_image(self, prompt, filename_prefix="image"):
        """Generuje obrázok pomocou DALL-E"""
        
        if not self.api_key:
            print("❌ API kľúč nie je nastavený!")
            return None
        
        try:
            print(f"🎨 Generujem obrázok...")
            print(f"📝 Prompt: {prompt[:100]}...")
            
            # Konverzia promptu
            dalle_prompt = self.convert_midjourney_to_dalle(prompt)
            print(f"🔄 DALL-E prompt: {dalle_prompt[:100]}...")
            
            # Generovanie obrázka
            response = openai.Image.create(
                prompt=dalle_prompt,
                n=1,
                size="1024x1024",
                quality="standard"
            )
            
            # Získanie URL obrázka
            image_url = response['data'][0]['url']
            print(f"✅ Obrázok vygenerovaný: {image_url}")
            
            # Stiahnutie obrázka
            image_response = requests.get(image_url)
            
            if image_response.status_code == 200:
                # Vytvorenie názvu súboru
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{filename_prefix}_{timestamp}.png"
                filepath = os.path.join(self.output_dir, filename)
                
                # Uloženie obrázka
                with open(filepath, 'wb') as f:
                    f.write(image_response.content)
                
                print(f"💾 Obrázok uložený: {filepath}")
                return filepath
            else:
                print(f"❌ Chyba pri sťahovaní obrázka: {image_response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Chyba pri generovaní: {e}")
            return None
    
    def test_with_sample_prompts(self):
        """Test s ukážkovými promptmi z Krvavý Dobšinský"""
        
        # Ukážkové prompty inšpirované Midjourney generátorom
        sample_prompts = [
            # Baba Jaga prompt
            "in dark forest, old house, witch hut featuring baba yaga, dead person with door, bones, horror, fire, dark horror atmosphere, cinematic lighting, dramatic shadows, highly detailed, photorealistic",
            
            # Upírsky prompt
            "in ancient castle, gothic architecture featuring vampire, with blood, cross, darkness, gothic atmosphere, dramatic lighting, medieval atmosphere, stone textures",
            
            # Vlkolak prompt
            "in dark forest, featuring werewolf, with bones, skull, darkness, storm, horror atmosphere, cinematic lighting, dramatic shadows, highly detailed",
            
            # Slovenský folklór
            "Slovak folk horror tale with witch, Slovak folklore, traditional clothing, rural setting, old house in village, mysterious atmosphere",
            
            # Minimalistický
            "minimalist horror scene featuring door, minimalist composition, stark contrast, single focal point, dramatic shadows, black and white"
        ]
        
        episode_names = [
            "Baba_Jaga_kanibal",
            "Upir_z_hradu", 
            "Vlkolak_v_lese",
            "Slovensky_folklor",
            "Minimalisticky_horor"
        ]
        
        print("🎨 === TEST GENEROVANIA OBRÁZKOV ===")
        print(f"📁 Obrázky sa uložia do: {self.output_dir}")
        print()
        
        for i, (prompt, episode) in enumerate(zip(sample_prompts, episode_names), 1):
            print(f"[{i}/5] Generujem: {episode}")
            
            filepath = self.generate_image(prompt, episode)
            
            if filepath:
                print(f"   ✅ Úspech: {os.path.basename(filepath)}")
            else:
                print(f"   ❌ Neúspech")
            
            print()
        
        print("🏁 Test dokončený!")
        print(f"📁 Skontrolujte priečinok: {self.output_dir}")

def main():
    """Hlavná funkcia"""
    print("🎨 OpenAI DALL-E Image Generator")
    print("Pre Krvavý Dobšinský podcast")
    print()
    
    # Kontrola API kľúča
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Chýba OPENAI_API_KEY!")
        print()
        print("Nastavte ho pomocou:")
        print("export OPENAI_API_KEY='sk-...'")
        print()
        print("Alebo vytvorte .env súbor s:")
        print("OPENAI_API_KEY=sk-...")
        return
    
    generator = OpenAIImageGenerator()
    
    # Test s ukážkovými promptmi
    generator.test_with_sample_prompts()

if __name__ == "__main__":
    main()
