#!/usr/bin/env python3
"""
Midjourney to Retro Style Converter
<PERSON><PERSON><PERSON><PERSON> existujúce Midjourney prompty a konvertuje ich do retro fairytale horror štýlu
"""

import os
import glob
from openai import OpenAI
import requests
from datetime import datetime
import time
import re

class MidjourneyToRetroConverter:
    def __init__(self, api_key):
        self.client = OpenAI(api_key=api_key)
        
        # Cesta k Midjourney promptom na ploche
        self.midjourney_dir = "/Users/<USER>/Desktop/Krvavý Audio/midjourney_prompts"
        
        # Výstupný priečinok na desktop
        desktop_path = os.path.expanduser("~/Desktop")
        self.output_dir = os.path.join(desktop_path, "Krvavý_Dobšinský_Retro_Artwork")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Retro fairytale horror štýl
        self.retro_style = """Stylized flat illustration in retro fairytale horror aesthetic, using a limited color palette of parchment beige (#D2B48C), blood red (#750000), dark brown (#2C1A0C), and small golden accents. Simplified geometric shapes, soft paper texture background, minimalist composition. Imagery should feel mysterious, folkloric, and slightly unsettling, inspired by Eastern European folk tales. No gradients, no photorealism – only vector-style silhouettes and symbolic elements. Consistent visual style for podcast episode artwork."""
    
    def find_midjourney_files(self):
        """Nájde všetky Midjourney prompt súbory"""
        
        print(f"🔍 Hľadám Midjourney prompty v: {self.midjourney_dir}")
        
        if not os.path.exists(self.midjourney_dir):
            print(f"❌ Priečinok neexistuje: {self.midjourney_dir}")
            
            # Skúsime nájsť všade na ploche
            print("🔍 Hľadám všade na ploche...")
            desktop_path = os.path.expanduser("~/Desktop")
            
            for root, dirs, files in os.walk(desktop_path):
                if "midjourney" in root.lower():
                    print(f"✅ Našiel som: {root}")
                    self.midjourney_dir = root
                    break
            else:
                print("❌ Nenašiel som Midjourney priečinok!")
                return []
        
        # Hľadanie TXT súborov
        txt_files = []
        patterns = [
            os.path.join(self.midjourney_dir, "*.txt"),
            os.path.join(self.midjourney_dir, "*midjourney*.txt"),
            os.path.join(self.midjourney_dir, "*prompt*.txt")
        ]
        
        for pattern in patterns:
            txt_files.extend(glob.glob(pattern))
        
        # Odstránenie duplikátov
        txt_files = list(set(txt_files))
        
        print(f"📊 Našiel som {len(txt_files)} súborov")
        
        return txt_files
    
    def extract_prompts_from_file(self, file_path):
        """Extrahuje prompty zo súboru"""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Hľadanie promptov v súbore
            prompts = []
            
            # Rôzne formáty promptov
            patterns = [
                r'🎨 KLASICKÝ HOROR:\s*(.+?)(?=\n\n|🏰|$)',
                r'KLASICKÝ HOROR:\s*(.+?)(?=\n\n|GOTICKÝ|$)',
                r'in .+?--ar.+?--v \d+',
                r'^[^🏰🎭⚫\n]+--ar.+?--v \d+',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                prompts.extend(matches)
            
            # Ak nenájdeme štruktúrované prompty, vezmeme celý obsah
            if not prompts and len(content) > 50:
                # Rozdelenie na riadky a filtrovanie
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    if (len(line) > 30 and 
                        not line.startswith('#') and 
                        not line.startswith('=') and
                        'MIDJOURNEY' not in line.upper() and
                        'PROMPT' not in line.upper()):
                        prompts.append(line)
            
            return [p.strip() for p in prompts if p.strip()]
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return []
    
    def convert_midjourney_to_retro(self, midjourney_prompt):
        """Konvertuje Midjourney prompt na retro štýl"""
        
        # Odstránenie Midjourney parametrov
        clean_prompt = re.sub(r'--\w+[^\s]*', '', midjourney_prompt).strip()
        
        # Odstránenie technických termínov
        replacements = {
            'photorealistic': '',
            '8k resolution': '',
            'cinematic lighting': 'dramatic lighting',
            'highly detailed': '',
            'dramatic shadows': 'simple shadows',
            ', ,': ',',
            '  ': ' '
        }
        
        for old, new in replacements.items():
            clean_prompt = clean_prompt.replace(old, new)
        
        # Skrátenie ak je príliš dlhý
        if len(clean_prompt) > 200:
            clean_prompt = clean_prompt[:200] + "..."
        
        # Pridanie retro štýlu
        return f"{clean_prompt}, {self.retro_style}"
    
    def generate_retro_image(self, episode_name, original_prompt):
        """Generuje retro obrázok"""
        
        print(f"🎨 Generujem: {episode_name}")
        print(f"📝 Originál: {original_prompt[:80]}...")
        
        # Konverzia na retro štýl
        retro_prompt = self.convert_midjourney_to_retro(original_prompt)
        
        try:
            # Generovanie
            response = self.client.images.generate(
                prompt=retro_prompt,
                n=1,
                size="1024x1024",
                quality="standard",
                model="dall-e-3"
            )
            
            image_url = response.data[0].url
            print(f"   ✅ Vygenerované!")
            
            # Stiahnutie a uloženie
            return self._save_image(image_url, episode_name)
            
        except Exception as e:
            print(f"   ❌ Chyba s DALL-E 3: {e}")
            
            # Pokus s DALL-E 2
            try:
                print(f"   🔄 Skúšam DALL-E 2...")
                response = self.client.images.generate(
                    prompt=retro_prompt,
                    n=1,
                    size="1024x1024",
                    model="dall-e-2"
                )
                
                image_url = response.data[0].url
                print(f"   ✅ Vygenerované (DALL-E 2)!")
                
                return self._save_image(image_url, episode_name)
                
            except Exception as e2:
                print(f"   ❌ Chyba aj s DALL-E 2: {e2}")
                return None
    
    def _save_image(self, image_url, episode_name):
        """Uloží obrázok"""
        try:
            image_response = requests.get(image_url, timeout=30)
            
            if image_response.status_code == 200:
                # Vytvorenie názvu súboru
                safe_name = re.sub(r'[^\w\-_]', '_', episode_name)
                filename = f"{safe_name}_retro_artwork.png"
                filepath = os.path.join(self.output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_response.content)
                
                print(f"   💾 Uložené: {filename}")
                return filepath
            else:
                print(f"   ❌ Chyba pri sťahovaní: {image_response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_all_midjourney_prompts(self):
        """Spracuje všetky Midjourney prompty"""
        
        print("🎨 === KONVERZIA MIDJOURNEY PROMPTOV NA RETRO ŠTÝL ===")
        print(f"📁 Zdroj: {self.midjourney_dir}")
        print(f"📁 Výstup: {self.output_dir}")
        print()
        
        # Nájdenie súborov
        txt_files = self.find_midjourney_files()
        
        if not txt_files:
            print("❌ Nenašli sa žiadne Midjourney súbory!")
            return
        
        print(f"📊 Spracovávam {len(txt_files)} súborov")
        print()
        
        successful = 0
        failed = 0
        total_prompts = 0
        
        for i, file_path in enumerate(txt_files, 1):
            filename = os.path.basename(file_path)
            episode_name = filename.replace('.txt', '').replace('_midjourney_prompts', '')
            
            print(f"[{i}/{len(txt_files)}] {filename}")
            
            # Extrakcia promptov
            prompts = self.extract_prompts_from_file(file_path)
            
            if prompts:
                print(f"   📝 Našiel som {len(prompts)} promptov")
                
                # Použijeme prvý prompt (klasický horor)
                first_prompt = prompts[0]
                total_prompts += 1
                
                result = self.generate_retro_image(episode_name, first_prompt)
                
                if result:
                    successful += 1
                else:
                    failed += 1
                
                # Pauza medzi generovaním
                if i < len(txt_files):
                    print("   ⏳ Pauza 3 sekundy...")
                    time.sleep(3)
            else:
                print(f"   ⚠️  Nenašli sa prompty v súbore")
                failed += 1
            
            print()
        
        # Finálny súhrn
        print("🏁 === VÝSLEDKY ===")
        print(f"📊 Spracované súbory: {len(txt_files)}")
        print(f"📝 Celkom promptov: {total_prompts}")
        print(f"✅ Úspešné: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📁 Obrázky uložené v: {self.output_dir}")
        
        return successful, failed

def main():
    """Hlavná funkcia"""
    print("🎨 === MIDJOURNEY TO RETRO CONVERTER ===")
    print("Konvertuje vaše existujúce Midjourney prompty do retro štýlu")
    print()
    
    # Kontrola API kľúča
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        return
    
    converter = MidjourneyToRetroConverter(api_key)
    
    print("🎭 Retro štýl:")
    print("• Farby: parchment beige, blood red, dark brown, golden accents")
    print("• Štýl: flat illustration, vector silhouettes")
    print("• Atmosféra: Eastern European folk tales")
    print()
    
    choice = input("Spustiť konverziu všetkých Midjourney promptov? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes', 'ano']:
        converter.process_all_midjourney_prompts()
    else:
        print("Ukončujem...")

if __name__ == "__main__":
    main()
